import { Test, TestingModule } from '@nestjs/testing';
import { BrandController } from './brands.controller';
import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { HttpException } from '@nestjs/common';

describe('BrandController', () => {
	let controller: BrandController;
	let service: any;
	let logger: any;

	beforeEach(async () => {
		const mockService = {
			createBrand: jest.fn(),
			getAllBrands: jest.fn(),
			getBrandById: jest.fn(),
			getBrandBySlug: jest.fn()
		};

		const mockLogger = {
			log: jest.fn(),
			error: jest.fn()
		};

		const module: TestingModule = await Test.createTestingModule({
			controllers: [BrandController],
			providers: [
				{
					provide: BrandService,
					useValue: mockService
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				}
			]
		}).compile();

		controller = module.get<BrandController>(BrandController);
		service = module.get<BrandService>(BrandService);
		logger = module.get<WinstonLogger>(WinstonLogger);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create', () => {
		it('should create a brand successfully', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Test Brand' };
			const mockBrand = { id: '1', name: 'Test Brand' };

			service.createBrand.mockResolvedValue(mockBrand);

			const result = await controller.create(createBrandDto);

			expect(service.createBrand).toHaveBeenCalledWith(createBrandDto);
			expect(result).toEqual(mockBrand);
		});

		it('should handle service errors', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Test Brand' };
			service.createBrand.mockRejectedValue(new Error('Service error'));

			await expect(controller.create(createBrandDto)).rejects.toThrow('Service error');
		});
	});

	describe('findbyId', () => {
		it('should return a brand by id', async () => {
			const mockBrand = { id: '1', name: 'Test Brand' };
			service.getBrandById.mockResolvedValue(mockBrand);

			const result = await controller.findbyId('1');

			expect(service.getBrandById).toHaveBeenCalledWith('1');
			expect(result).toEqual(mockBrand);
		});

		it('should return null when brand not found', async () => {
			service.getBrandById.mockResolvedValue(null);

			const result = await controller.findbyId('nonexistent');

			expect(result).toBeNull();
		});
	});

	describe('findAll', () => {
		it('should return paginated brands with default parameters', async () => {
			const mockResult = { brands: [{ id: '1', name: 'Test Brand' }], total: 1 };
			service.getAllBrands.mockResolvedValue(mockResult);

			const result = await controller.findAll();

			expect(logger.log).toHaveBeenCalledWith('Fetching all brands', {
				page: 1,
				limit: 10,
				orderBy: 'DESC'
			});
			expect(service.getAllBrands).toHaveBeenCalledWith(1, 10, '', 'DESC');
			expect(result).toEqual(mockResult);
		});

		it('should handle service errors and throw HttpException', async () => {
			service.getAllBrands.mockRejectedValue(new Error('Service error'));

			await expect(controller.findAll()).rejects.toThrow(HttpException);
			expect(logger.error).toHaveBeenCalledWith('Error fetching brands', {
				error: expect.any(Error)
			});
		});
	});

	describe('findBySlug', () => {
		it('should return brand by slug', async () => {
			const mockBrand = { id: '1', name: 'Test Brand', slug: 'test-brand' };
			service.getBrandBySlug.mockResolvedValue(mockBrand);

			const result = await controller.findBySlug('test-brand');

			expect(service.getBrandBySlug).toHaveBeenCalledWith('test-brand');
			expect(result).toEqual(mockBrand);
		});

		it('should return null when brand not found', async () => {
			service.getBrandBySlug.mockResolvedValue(null);

			const result = await controller.findBySlug('nonexistent');

			expect(result).toBeNull();
		});
	});
});
